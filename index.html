<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <style>
    body {
      font-family: sans-serif;
      display: flex;
      gap: 20px;
    }

    .product,
    .car {
      width: 800px;
      border: 1px solid #ccc;
      padding: 10px;
      border-radius: 5px;
    }

    .productListItem,
    .anyProductItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 5px 0;
      border-bottom: 1px solid #eee;
    }

    .anyProductItem span {
      min-width: 120px;
      text-align: center;
    }

    .anyProductItem span.name {
      min-width: 200px;
      text-align: left;
    }

    .anyProductItem .num button {
      margin: 0 5px;
    }

    .noProduct {
      color: #999;
      text-align: center;
      padding: 20px;
    }

    .sumNumber,
    .sumMoney {
      text-align: right;
      margin-top: 10px;
      font-size: 18px;
      font-weight: bold;
    }
  </style>
</head>

<body>
  <div id="app">
    <div id="product" class="product">
      <h2>商品列表</h2>
      <div class="productList">
        <div v-for="item in itProduct" :key="item.id" class="productListItem">
          <span>{{ item.name }} - ￥{{formatCurrency(item.price) }} (库存: {{item.stock}})</span>
          <button @click="addToCart(item)" :disabled="item.stock === 0">加入购物车</button>
        </div>
      </div>
    </div>
    <div class="car">
      <h2>我的购物车</h2>
      <div class="carList">
        <div v-if="cartList.length > 0">
          <div class="anyProductItem">
            <span class="name">商品名称</span>
            <span class="price">单价</span>
            <span class="num">数量</span>
            <span class="amount">小计金额</span>
            <span class="action">操作</span>
          </div>
          <div v-for="item in cartList" :key="'cart-'+item.id" class="anyProductItem">
            <span class="name">{{ item.name }}</span>
            <span class="price">￥{{formatCurrency(item.price) }}</span>
            <span class="num">
              <button @click="decQuantity(item)" :disabled="item.count <= 1">-</button>
              {{ item.count }}
              <button @click="addToCart(item)">+</button>
            </span>
            <span class="amount">￥{{ formatCurrency(item.price * item.count) }}</span>
            <button @click="removeFromCart(item.id)">移除</button>
          </div>
          <div class="sumNumber">总数量：{{ totalCount }}</div>
          <div class="sumMoney">总金额：￥{{ formatCurrency(totalPrice)}}</div>
          <div v-if="shippingDiscount" style="color: green; text-align: right; margin-top: 5px;">
            恭喜！您已达到满200免运费标准！
          </div>
        </div>
        <div v-else class="noProduct">
          <p>购物车是空的哦～</p>
        </div>
      </div>
    </div>
  </div>

  <script src="https://fastly.jsdelivr.net/npm/vue@2/dist/vue.js"></script>
  <script>
    console.log();
    const app = new Vue({
      el: '#app',
      data: {
        itProduct: [
          { id: 1, name: "Vue.js 实战", price: 89, stock: 10 },
          { id: 2, name: "JavaScript高级程序设计", price: 129, stock: 10 },
          { id: 3, name: "深入浅出Node.js", price: 78, stock: 10 },
          { id: 4, name: "代码整洁之道", price: 58, stock: 10 }
        ],
        cartList: [],
        shippingDiscount: false
      },
      methods: {
        formatCurrency(value) {
          if (typeof value !== 'number') {
            return value;
          }
          return + value.toFixed(2);
        },
        addToCart(product) {
          const existingProduct = this.cartList.find(item => item.id === product.id);
          console.log('existingProduct: ', existingProduct);
          if (existingProduct) {
            if (existingProduct.count < product.stock) {
              existingProduct.count++;
            } else {
              alert('该商品库存不足啦！');
            }
          } else {
            if (product.stock > 0) {
              this.cartList.push({
                ...product,
                count: 1
              });
            } else {
              alert('该商品已售罄！');
            }
          }
        },
        // incQuantity(item) {
        //     const originalProduct = this.itProduct.find(p => p.id === item.id);
        //     if (originalProduct && item.count < originalProduct.stock) {
        //         item.count++;
        //     } else {
        //         alert('库存不足，无法再增加了哦！');
        //     }
        // },
        decQuantity(item) {
          if (item.count > 1) {
            item.count--;
          }
        },
        removeFromCart(productId) {
          this.cartList = this.cartList.filter(item => item.id !== productId);
        },
        // 为了让模板更清晰，把判断逻辑封装成一个方法
        isStockExceeded(cartItem) {
          const originalProduct = this.itProduct.find(p => p.id === cartItem.id);
          return originalProduct && cartItem.count >= originalProduct.stock;
        }
      },
      computed: {
        totalCount() {
          return this.cartList.reduce((sum, item) => sum + item.count, 0);
        },
        totalPrice() {
          return this.cartList.reduce((sum, item) => sum + (item.price * item.count), 0);
        },
      },
      watch: {
        totalPrice(newValue) {
          this.shippingDiscount = newValue >= 200;
        }
      }
    });
  </script>
</body>

</html>
